class Patient {
  final int? id;
  final int userId;
  final String name;
  final DateTime dateOfBirth;
  final String gender;
  final String bloodGroup;
  final List<String> allergies;
  final List<String> chronicDiseases;
  final Map<String, String> emergencyContact; // name, relationship, phone
  final String address;
  final String? profileImageUrl;
  final DateTime createdAt;
  final DateTime lastUpdated;
  final Map<String, String> contactInfo; // email, phone
  final Map<String, dynamic>? insuranceInfo; // provider, policyNumber, expiryDate

  Patient({
    this.id,
    required this.userId,
    required this.name,
    required this.dateOfBirth,
    required this.gender,
    required this.bloodGroup,
    required this.allergies,
    required this.chronicDiseases,
    required this.emergencyContact,
    required this.address,
    this.profileImageUrl,
    required this.createdAt,
    required this.lastUpdated,
    required this.contactInfo,
    this.insuranceInfo,
  });

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'user_id': userId,
      'name': name,
      'date_of_birth': dateOfBirth.millisecondsSinceEpoch,
      'gender': gender,
      'blood_group': bloodGroup,
      'allergies': allergies.join(','),
      'chronic_diseases': chronicDiseases.join(','),
      'emergency_contact_name': emergencyContact['name'],
      'emergency_contact_relationship': emergencyContact['relationship'],
      'emergency_contact_phone': emergencyContact['phone'],
      'address': address,
      'profile_image_url': profileImageUrl,
      'created_at': createdAt.millisecondsSinceEpoch,
      'last_updated': lastUpdated.millisecondsSinceEpoch,
      'contact_email': contactInfo['email'],
      'contact_phone': contactInfo['phone'],
      'insurance_provider': insuranceInfo?['provider'],
      'insurance_policy_number': insuranceInfo?['policyNumber'],
      'insurance_expiry_date': insuranceInfo?['expiryDate'] != null 
          ? (insuranceInfo!['expiryDate'] as DateTime).millisecondsSinceEpoch
          : null,
    };
  }

  factory Patient.fromMap(Map<String, dynamic> map) {
    return Patient(
      id: map['id']?.toInt(),
      userId: map['user_id']?.toInt() ?? 0,
      name: map['name'] ?? '',
      dateOfBirth: DateTime.fromMillisecondsSinceEpoch(map['date_of_birth']),
      gender: map['gender'] ?? '',
      bloodGroup: map['blood_group'] ?? '',
      allergies: map['allergies']?.split(',') ?? [],
      chronicDiseases: map['chronic_diseases']?.split(',') ?? [],
      emergencyContact: {
        'name': map['emergency_contact_name'] ?? '',
        'relationship': map['emergency_contact_relationship'] ?? '',
        'phone': map['emergency_contact_phone'] ?? '',
      },
      address: map['address'] ?? '',
      profileImageUrl: map['profile_image_url'],
      createdAt: DateTime.fromMillisecondsSinceEpoch(map['created_at']),
      lastUpdated: DateTime.fromMillisecondsSinceEpoch(map['last_updated']),
      contactInfo: {
        'email': map['contact_email'] ?? '',
        'phone': map['contact_phone'] ?? '',
      },
      insuranceInfo: map['insurance_provider'] != null ? {
        'provider': map['insurance_provider'],
        'policyNumber': map['insurance_policy_number'],
        'expiryDate': map['insurance_expiry_date'] != null 
            ? DateTime.fromMillisecondsSinceEpoch(map['insurance_expiry_date'])
            : null,
      } : null,
    );
  }
}
