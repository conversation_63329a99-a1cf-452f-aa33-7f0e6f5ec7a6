import 'package:flutter/material.dart';
import 'package:flutter_neumorphic_plus/flutter_neumorphic.dart';
import 'package:provider/provider.dart';
import 'screens/splash_screen.dart';
import 'services/auth_service.dart';
import 'services/database_initializer.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  // Initialize SQLite database and create sample data
  try {
    final databaseInitializer = DatabaseInitializer();
    await databaseInitializer.initializeSampleData();
    debugPrint('Database initialized successfully');
  } catch (e) {
    debugPrint('Error initializing database: $e');
  }

  runApp(
    MultiProvider(
      providers: [ChangeNotifierProvider(create: (_) => AuthService())],
      child: const MyApp(),
    ),
  );
}

class MyApp extends StatelessWidget {
  const MyApp({super.key});

  @override
  Widget build(BuildContext context) {
    return NeumorphicApp(
      debugShowCheckedModeBanner: false,
      title: 'Doctor Appointment App',
      theme: NeumorphicThemeData(
        baseColor: const Color(0xFFE0E5EC), // soft white-grey
        lightSource: LightSource.topLeft,
        depth: 8,
      ),
      home: const SplashScreen(),
    );
  }
}
