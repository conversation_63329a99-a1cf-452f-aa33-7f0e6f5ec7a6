class User {
  final int? id;
  final String email;
  final String passwordHash;
  final String name;
  final String role; // 'doctor', 'patient', 'admin'
  final DateTime createdAt;
  final DateTime? lastLogin;
  final bool isActive;
  final String? profileImageUrl;
  final String? phoneNumber;

  User({
    this.id,
    required this.email,
    required this.passwordHash,
    required this.name,
    required this.role,
    required this.createdAt,
    this.lastLogin,
    this.isActive = true,
    this.profileImageUrl,
    this.phoneNumber,
  });

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'email': email,
      'password_hash': passwordHash,
      'name': name,
      'role': role,
      'created_at': createdAt.millisecondsSinceEpoch,
      'last_login': lastLogin?.millisecondsSinceEpoch,
      'is_active': isActive ? 1 : 0,
      'profile_image_url': profileImageUrl,
      'phone_number': phoneNumber,
    };
  }

  factory User.fromMap(Map<String, dynamic> map) {
    return User(
      id: map['id']?.toInt(),
      email: map['email'] ?? '',
      passwordHash: map['password_hash'] ?? '',
      name: map['name'] ?? '',
      role: map['role'] ?? '',
      createdAt: DateTime.fromMillisecondsSinceEpoch(map['created_at']),
      lastLogin: map['last_login'] != null 
          ? DateTime.fromMillisecondsSinceEpoch(map['last_login'])
          : null,
      isActive: map['is_active'] == 1,
      profileImageUrl: map['profile_image_url'],
      phoneNumber: map['phone_number'],
    );
  }

  User copyWith({
    int? id,
    String? email,
    String? passwordHash,
    String? name,
    String? role,
    DateTime? createdAt,
    DateTime? lastLogin,
    bool? isActive,
    String? profileImageUrl,
    String? phoneNumber,
  }) {
    return User(
      id: id ?? this.id,
      email: email ?? this.email,
      passwordHash: passwordHash ?? this.passwordHash,
      name: name ?? this.name,
      role: role ?? this.role,
      createdAt: createdAt ?? this.createdAt,
      lastLogin: lastLogin ?? this.lastLogin,
      isActive: isActive ?? this.isActive,
      profileImageUrl: profileImageUrl ?? this.profileImageUrl,
      phoneNumber: phoneNumber ?? this.phoneNumber,
    );
  }
}
