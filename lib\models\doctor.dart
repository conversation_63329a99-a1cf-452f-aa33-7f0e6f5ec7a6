class Doctor {
  final int? id;
  final int userId;
  final String name;
  final String specialization;
  final int experience;
  final List<String> qualifications;
  final String bio;
  final String? profileImageUrl;
  final String clinicAddress;
  final double consultationFee;
  final double rating;
  final int totalReviews;
  final List<String> availableDays;
  final Map<String, String> workingHours; // start, end
  final DateTime createdAt;
  final bool isVerified;
  final bool isActive;
  final Map<String, String> contactInfo; // email, phone

  Doctor({
    this.id,
    required this.userId,
    required this.name,
    required this.specialization,
    required this.experience,
    required this.qualifications,
    required this.bio,
    this.profileImageUrl,
    required this.clinicAddress,
    required this.consultationFee,
    this.rating = 0.0,
    this.totalReviews = 0,
    required this.availableDays,
    required this.workingHours,
    required this.createdAt,
    this.isVerified = false,
    this.isActive = true,
    required this.contactInfo,
  });

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'user_id': userId,
      'name': name,
      'specialization': specialization,
      'experience': experience,
      'qualifications': qualifications.join(','),
      'bio': bio,
      'profile_image_url': profileImageUrl,
      'clinic_address': clinicAddress,
      'consultation_fee': consultationFee,
      'rating': rating,
      'total_reviews': totalReviews,
      'available_days': availableDays.join(','),
      'working_hours_start': workingHours['start'],
      'working_hours_end': workingHours['end'],
      'created_at': createdAt.millisecondsSinceEpoch,
      'is_verified': isVerified ? 1 : 0,
      'is_active': isActive ? 1 : 0,
      'contact_email': contactInfo['email'],
      'contact_phone': contactInfo['phone'],
    };
  }

  factory Doctor.fromMap(Map<String, dynamic> map) {
    return Doctor(
      id: map['id']?.toInt(),
      userId: map['user_id']?.toInt() ?? 0,
      name: map['name'] ?? '',
      specialization: map['specialization'] ?? '',
      experience: map['experience']?.toInt() ?? 0,
      qualifications: map['qualifications']?.split(',') ?? [],
      bio: map['bio'] ?? '',
      profileImageUrl: map['profile_image_url'],
      clinicAddress: map['clinic_address'] ?? '',
      consultationFee: map['consultation_fee']?.toDouble() ?? 0.0,
      rating: map['rating']?.toDouble() ?? 0.0,
      totalReviews: map['total_reviews']?.toInt() ?? 0,
      availableDays: map['available_days']?.split(',') ?? [],
      workingHours: {
        'start': map['working_hours_start'] ?? '',
        'end': map['working_hours_end'] ?? '',
      },
      createdAt: DateTime.fromMillisecondsSinceEpoch(map['created_at']),
      isVerified: map['is_verified'] == 1,
      isActive: map['is_active'] == 1,
      contactInfo: {
        'email': map['contact_email'] ?? '',
        'phone': map['contact_phone'] ?? '',
      },
    );
  }
}
