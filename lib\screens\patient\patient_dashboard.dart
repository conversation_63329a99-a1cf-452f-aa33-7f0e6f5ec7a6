import 'package:flutter_neumorphic_plus/flutter_neumorphic.dart';
import 'package:provider/provider.dart';
import 'package:intl/intl.dart';
import '../../services/auth_service.dart';
import '../../services/database_service.dart';
import '../../models/appointment.dart';
import '../../models/patient.dart';
import '../../widgets/custom_app_bar.dart';
import '../login_screen.dart';
import 'book_appointment.dart';
import 'appointment_history.dart';

class PatientDashboard extends StatefulWidget {
  const PatientDashboard({super.key});

  @override
  State<PatientDashboard> createState() => _PatientDashboardState();
}

class _PatientDashboardState extends State<PatientDashboard> {
  final DatabaseService _databaseService = DatabaseService();
  List<Appointment> _upcomingAppointments = [];
  Patient? _patientProfile;
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _loadPatientData();
  }

  Future<void> _loadPatientData() async {
    final authService = Provider.of<AuthService>(context, listen: false);
    final user = authService.user;

    if (user != null) {
      try {
        // Load patient profile
        _patientProfile = await _databaseService.getPatientByUserId(user.id!);

        // Load upcoming appointments
        if (_patientProfile != null) {
          _upcomingAppointments = await _databaseService
              .getUpcomingAppointments(_patientProfile!.id!);
        }
      } catch (e) {
        debugPrint('Error loading patient data: $e');
      }
    }

    if (mounted) {
      setState(() {
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    final authService = Provider.of<AuthService>(context);
    final user = authService.user;

    return Scaffold(
      backgroundColor: const Color(0xFFE0E5EC),
      appBar: CustomAppBar(
        title: 'Patient Dashboard',
        actions: [
          NeumorphicButton(
            style: const NeumorphicStyle(
              boxShape: NeumorphicBoxShape.circle(),
              depth: 2,
            ),
            margin: const EdgeInsets.only(right: 8),
            padding: const EdgeInsets.all(8),
            onPressed: () async {
              await authService.signOut();
              if (!mounted) return;
              Navigator.of(context).pushReplacement(
                MaterialPageRoute(builder: (_) => const LoginScreen()),
              );
            },
            child: const Icon(Icons.logout, size: 20),
          ),
        ],
      ),
      body: SafeArea(
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Welcome Section
              Neumorphic(
                style: NeumorphicStyle(
                  depth: 4,
                  intensity: 0.7,
                  boxShape: NeumorphicBoxShape.roundRect(
                    BorderRadius.circular(12),
                  ),
                ),
                child: Padding(
                  padding: const EdgeInsets.all(16),
                  child: Row(
                    children: [
                      Neumorphic(
                        style: const NeumorphicStyle(
                          boxShape: NeumorphicBoxShape.circle(),
                          depth: 4,
                        ),
                        child: Container(
                          width: 60,
                          height: 60,
                          decoration: const BoxDecoration(
                            shape: BoxShape.circle,
                            color: Colors.blue,
                          ),
                          child: const Icon(
                            Icons.person,
                            color: Colors.white,
                            size: 30,
                          ),
                        ),
                      ),
                      const SizedBox(width: 16),
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              user?.name ?? 'Patient',
                              style: const TextStyle(
                                fontSize: 20,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                            const SizedBox(height: 4),
                            Text(
                              user?.email ?? '',
                              style: const TextStyle(
                                fontSize: 14,
                                color: Colors.grey,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
              ),

              const SizedBox(height: 24),

              // Action Buttons
              Row(
                children: [
                  Expanded(
                    child: _buildActionButton(
                      'Book Appointment',
                      Icons.add_circle_outline,
                      () {
                        Navigator.of(context).push(
                          MaterialPageRoute(
                            builder: (_) => const BookAppointment(),
                          ),
                        );
                      },
                    ),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: _buildActionButton('History', Icons.history, () {
                      Navigator.of(context).push(
                        MaterialPageRoute(
                          builder: (_) => const AppointmentHistory(),
                        ),
                      );
                    }),
                  ),
                ],
              ),

              const SizedBox(height: 24),

              // Upcoming Appointments
              const Text(
                'Upcoming Appointments',
                style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
              ),
              const SizedBox(height: 12),

              Expanded(
                child:
                    _isLoading
                        ? const Center(child: CircularProgressIndicator())
                        : _upcomingAppointments.isEmpty
                        ? Center(
                          child: Neumorphic(
                            style: NeumorphicStyle(
                              depth: 4,
                              intensity: 0.7,
                              boxShape: NeumorphicBoxShape.roundRect(
                                BorderRadius.circular(12),
                              ),
                            ),
                            child: Padding(
                              padding: const EdgeInsets.all(24),
                              child: Column(
                                mainAxisSize: MainAxisSize.min,
                                children: [
                                  Icon(
                                    Icons.calendar_today_outlined,
                                    size: 48,
                                    color: Colors.grey[400],
                                  ),
                                  const SizedBox(height: 16),
                                  Text(
                                    'No upcoming appointments',
                                    style: TextStyle(
                                      fontSize: 16,
                                      color: Colors.grey[600],
                                    ),
                                  ),
                                  const SizedBox(height: 8),
                                  Text(
                                    'Book your first appointment with a doctor',
                                    style: TextStyle(
                                      fontSize: 14,
                                      color: Colors.grey[500],
                                    ),
                                    textAlign: TextAlign.center,
                                  ),
                                ],
                              ),
                            ),
                          ),
                        )
                        : ListView.builder(
                          itemCount: _upcomingAppointments.length,
                          itemBuilder: (context, index) {
                            final appointment = _upcomingAppointments[index];
                            final formattedDate = DateFormat(
                              'MMM dd, yyyy',
                            ).format(appointment.dateTime);
                            final formattedTime = DateFormat(
                              'hh:mm a',
                            ).format(appointment.dateTime);

                            return Padding(
                              padding: const EdgeInsets.only(bottom: 12),
                              child: Neumorphic(
                                style: NeumorphicStyle(
                                  depth: 4,
                                  intensity: 0.7,
                                  boxShape: NeumorphicBoxShape.roundRect(
                                    BorderRadius.circular(12),
                                  ),
                                ),
                                child: Padding(
                                  padding: const EdgeInsets.all(16),
                                  child: Row(
                                    children: [
                                      Container(
                                        width: 4,
                                        height: 50,
                                        decoration: BoxDecoration(
                                          color: Colors.blue,
                                          borderRadius: BorderRadius.circular(
                                            4,
                                          ),
                                        ),
                                      ),
                                      const SizedBox(width: 16),
                                      Expanded(
                                        child: Column(
                                          crossAxisAlignment:
                                              CrossAxisAlignment.start,
                                          children: [
                                            Text(
                                              appointment.doctorName,
                                              style: const TextStyle(
                                                fontSize: 16,
                                                fontWeight: FontWeight.bold,
                                              ),
                                            ),
                                            const SizedBox(height: 4),
                                            Row(
                                              children: [
                                                const Icon(
                                                  Icons.calendar_today,
                                                  size: 14,
                                                  color: Colors.grey,
                                                ),
                                                const SizedBox(width: 4),
                                                Text(
                                                  formattedDate,
                                                  style: const TextStyle(
                                                    fontSize: 14,
                                                    color: Colors.grey,
                                                  ),
                                                ),
                                                const SizedBox(width: 12),
                                                const Icon(
                                                  Icons.access_time,
                                                  size: 14,
                                                  color: Colors.grey,
                                                ),
                                                const SizedBox(width: 4),
                                                Text(
                                                  formattedTime,
                                                  style: const TextStyle(
                                                    fontSize: 14,
                                                    color: Colors.grey,
                                                  ),
                                                ),
                                              ],
                                            ),
                                          ],
                                        ),
                                      ),
                                      NeumorphicButton(
                                        style: const NeumorphicStyle(
                                          depth: 4,
                                          boxShape: NeumorphicBoxShape.circle(),
                                        ),
                                        padding: const EdgeInsets.all(8),
                                        onPressed:
                                            () => _cancelAppointment(
                                              appointment.id!,
                                            ),
                                        child: const Icon(
                                          Icons.close,
                                          size: 16,
                                          color: Colors.red,
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                              ),
                            );
                          },
                        ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildActionButton(
    String title,
    IconData icon,
    VoidCallback onPressed,
  ) {
    return NeumorphicButton(
      style: NeumorphicStyle(
        depth: 4,
        intensity: 0.7,
        boxShape: NeumorphicBoxShape.roundRect(BorderRadius.circular(12)),
      ),
      padding: const EdgeInsets.all(16),
      onPressed: onPressed,
      child: Column(
        children: [
          Icon(icon, size: 32, color: Colors.blue),
          const SizedBox(height: 8),
          Text(
            title,
            style: const TextStyle(fontSize: 14, fontWeight: FontWeight.w500),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Future<void> _cancelAppointment(int appointmentId) async {
    try {
      // Get the appointment to retrieve the slot ID
      final appointments = await _databaseService.getAppointmentsByPatientId(
        _patientProfile!.id!,
      );
      final appointment = appointments.firstWhere((a) => a.id == appointmentId);

      if (appointment.slotId != null) {
        // Update the slot to be available again
        final slot = await _databaseService.getSlotById(appointment.slotId!);
        if (slot != null) {
          final updatedSlot = slot.copyWith(isBooked: false, patientId: null);
          await _databaseService.updateSlot(updatedSlot);
        }
      }

      // Update appointment status to cancelled
      final updatedAppointment = Appointment(
        id: appointment.id,
        slotId: appointment.slotId,
        patientId: appointment.patientId,
        doctorId: appointment.doctorId,
        dateTime: appointment.dateTime,
        durationMinutes: appointment.durationMinutes,
        patientName: appointment.patientName,
        doctorName: appointment.doctorName,
        status: 'cancelled',
        symptoms: appointment.symptoms,
        notes: appointment.notes,
        followUpDate: appointment.followUpDate,
        createdAt: appointment.createdAt,
        lastUpdated: DateTime.now(),
        paymentStatus: appointment.paymentStatus,
        paymentAmount: appointment.paymentAmount,
        paymentMethod: appointment.paymentMethod,
        meetingType: appointment.meetingType,
        meetingLink: appointment.meetingLink,
      );

      await _databaseService.updateAppointment(updatedAppointment);

      if (!mounted) return;

      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Appointment cancelled successfully')),
      );

      // Reload data
      _loadPatientData();
    } catch (e) {
      if (!mounted) return;

      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Error cancelling appointment: $e')),
      );
    }
  }
}
