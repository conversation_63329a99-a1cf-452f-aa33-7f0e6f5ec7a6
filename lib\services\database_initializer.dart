import 'package:flutter/material.dart';
import 'package:crypto/crypto.dart';
import 'dart:convert';
import '../models/user.dart';
import '../models/doctor.dart';
import '../models/patient.dart';
import '../models/slot.dart';
import '../models/appointment.dart';
import 'database_service.dart';

class DatabaseInitializer {
  final DatabaseService _databaseService = DatabaseService();

  String _hashPassword(String password) {
    final bytes = utf8.encode(password);
    final digest = sha256.convert(bytes);
    return digest.toString();
  }

  Future<void> initializeSampleData() async {
    try {
      // Check if data already exists
      final existingUsers = await _databaseService.getAllDoctors();
      if (existingUsers.isNotEmpty) {
        debugPrint('Sample data already exists');
        return;
      }

      await _createSampleUsers();
      debugPrint('Sample data initialized successfully');
    } catch (e) {
      debugPrint('Error initializing sample data: $e');
    }
  }

  Future<void> _createSampleUsers() async {
    // Create sample doctor user
    final doctorUser = User(
      email: '<EMAIL>',
      passwordHash: _hashPassword('password123'),
      name: 'Dr. <PERSON>',
      role: 'doctor',
      createdAt: DateTime.now(),
      phoneNumber: '+**********',
    );

    final doctorUserId = await _databaseService.insertUser(doctorUser);

    // Create doctor profile
    final doctor = Doctor(
      userId: doctorUserId,
      name: 'Dr. John Smith',
      specialization: 'Cardiologist',
      experience: 10,
      qualifications: ['MBBS', 'MD', 'DM'],
      bio: 'Experienced cardiologist with 10+ years of practice',
      clinicAddress: '123 Medical Center, New York',
      consultationFee: 100.0,
      rating: 4.8,
      totalReviews: 120,
      availableDays: ['Monday', 'Wednesday', 'Friday'],
      workingHours: {
        'start': '09:00',
        'end': '17:00',
      },
      createdAt: DateTime.now(),
      isVerified: true,
      isActive: true,
      contactInfo: {
        'email': '<EMAIL>',
        'phone': '+**********',
      },
    );

    final doctorId = await _databaseService.insertDoctor(doctor);

    // Create sample patient user
    final patientUser = User(
      email: '<EMAIL>',
      passwordHash: _hashPassword('password123'),
      name: 'Jane Doe',
      role: 'patient',
      createdAt: DateTime.now(),
      phoneNumber: '+**********',
    );

    final patientUserId = await _databaseService.insertUser(patientUser);

    // Create patient profile
    final patient = Patient(
      userId: patientUserId,
      name: 'Jane Doe',
      dateOfBirth: DateTime(1990, 5, 15),
      gender: 'Female',
      bloodGroup: 'O+',
      allergies: ['Penicillin'],
      chronicDiseases: ['Hypertension'],
      emergencyContact: {
        'name': 'John Doe',
        'relationship': 'Spouse',
        'phone': '+**********',
      },
      address: '456 Residential Ave, New York',
      createdAt: DateTime.now(),
      lastUpdated: DateTime.now(),
      contactInfo: {
        'email': '<EMAIL>',
        'phone': '+**********',
      },
      insuranceInfo: {
        'provider': 'Health Insurance Co.',
        'policyNumber': 'HI12345678',
        'expiryDate': DateTime(2025, 12, 31),
      },
    );

    final patientId = await _databaseService.insertPatient(patient);

    // Create sample slots for the doctor
    await _createSampleSlots(doctorId);

    // Create a sample appointment
    await _createSampleAppointment(doctorId, patientId, doctorUserId, patientUserId);
  }

  Future<void> _createSampleSlots(int doctorId) async {
    final now = DateTime.now();
    final slots = <Slot>[];

    // Create slots for the next 7 days
    for (int day = 1; day <= 7; day++) {
      final date = now.add(Duration(days: day));
      
      // Skip weekends for this example
      if (date.weekday == DateTime.saturday || date.weekday == DateTime.sunday) {
        continue;
      }

      // Create morning slots (9 AM to 12 PM)
      for (int hour = 9; hour < 12; hour++) {
        final slotTime = DateTime(date.year, date.month, date.day, hour, 0);
        slots.add(Slot(
          doctorId: doctorId,
          dateTime: slotTime,
          durationMinutes: 30,
          createdAt: now,
        ));

        // Add 30-minute slot
        final slotTime30 = DateTime(date.year, date.month, date.day, hour, 30);
        slots.add(Slot(
          doctorId: doctorId,
          dateTime: slotTime30,
          durationMinutes: 30,
          createdAt: now,
        ));
      }

      // Create afternoon slots (2 PM to 5 PM)
      for (int hour = 14; hour < 17; hour++) {
        final slotTime = DateTime(date.year, date.month, date.day, hour, 0);
        slots.add(Slot(
          doctorId: doctorId,
          dateTime: slotTime,
          durationMinutes: 30,
          createdAt: now,
        ));

        // Add 30-minute slot
        final slotTime30 = DateTime(date.year, date.month, date.day, hour, 30);
        slots.add(Slot(
          doctorId: doctorId,
          dateTime: slotTime30,
          durationMinutes: 30,
          createdAt: now,
        ));
      }
    }

    // Insert all slots
    for (final slot in slots) {
      await _databaseService.insertSlot(slot);
    }
  }

  Future<void> _createSampleAppointment(int doctorId, int patientId, int doctorUserId, int patientUserId) async {
    final appointmentTime = DateTime.now().add(const Duration(days: 2, hours: 10));
    
    final appointment = Appointment(
      patientId: patientId,
      doctorId: doctorId,
      dateTime: appointmentTime,
      durationMinutes: 30,
      patientName: 'Jane Doe',
      doctorName: 'Dr. John Smith',
      status: 'scheduled',
      symptoms: 'Chest pain and shortness of breath',
      notes: 'Patient has a history of hypertension',
      createdAt: DateTime.now(),
      lastUpdated: DateTime.now(),
      paymentStatus: 'pending',
      paymentAmount: 100.0,
      paymentMethod: 'credit_card',
      meetingType: 'in-person',
    );

    await _databaseService.insertAppointment(appointment);
  }
}
