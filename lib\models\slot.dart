class Slot {
  final int? id;
  final int doctorId;
  final DateTime dateTime;
  final int durationMinutes;
  final bool isBooked;
  final int? patientId;
  final DateTime createdAt;

  Slot({
    this.id,
    required this.doctorId,
    required this.dateTime,
    this.durationMinutes = 30,
    this.isBooked = false,
    this.patientId,
    required this.createdAt,
  });

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'doctor_id': doctorId,
      'date_time': dateTime.millisecondsSinceEpoch,
      'duration_minutes': durationMinutes,
      'is_booked': isBooked ? 1 : 0,
      'patient_id': patientId,
      'created_at': createdAt.millisecondsSinceEpoch,
    };
  }

  factory Slot.fromMap(Map<String, dynamic> map) {
    return Slot(
      id: map['id']?.toInt(),
      doctorId: map['doctor_id']?.toInt() ?? 0,
      dateTime: DateTime.fromMillisecondsSinceEpoch(map['date_time']),
      durationMinutes: map['duration_minutes']?.toInt() ?? 30,
      isBooked: map['is_booked'] == 1,
      patientId: map['patient_id']?.toInt(),
      createdAt: DateTime.fromMillisecondsSinceEpoch(map['created_at']),
    );
  }

  Slot copyWith({
    int? id,
    int? doctorId,
    DateTime? dateTime,
    int? durationMinutes,
    bool? isBooked,
    int? patientId,
    DateTime? createdAt,
  }) {
    return Slot(
      id: id ?? this.id,
      doctorId: doctorId ?? this.doctorId,
      dateTime: dateTime ?? this.dateTime,
      durationMinutes: durationMinutes ?? this.durationMinutes,
      isBooked: isBooked ?? this.isBooked,
      patientId: patientId ?? this.patientId,
      createdAt: createdAt ?? this.createdAt,
    );
  }
}
