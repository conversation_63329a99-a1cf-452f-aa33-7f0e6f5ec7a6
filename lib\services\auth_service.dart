import 'package:flutter/material.dart';
import 'package:crypto/crypto.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'dart:convert';
import '../models/user.dart' as models;
import 'database_service.dart';

enum UserRole { doctor, patient }

class AuthService extends ChangeNotifier {
  final DatabaseService _databaseService = DatabaseService();
  models.User? _user;
  UserRole? _userRole;
  bool _isLoading = false;

  AuthService() {
    _loadUserFromPreferences();
  }

  models.User? get user => _user;
  UserRole? get userRole => _userRole;
  bool get isLoading => _isLoading;
  bool get isAuthenticated => _user != null;

  void _setLoading(bool value) {
    _isLoading = value;
    notifyListeners();
  }

  Future<void> _loadUserFromPreferences() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final userId = prefs.getInt('user_id');
      if (userId != null) {
        _user = await _databaseService.getUserById(userId);
        if (_user != null) {
          _userRole =
              _user!.role == 'doctor' ? UserRole.doctor : UserRole.patient;
        }
        notifyListeners();
      }
    } catch (e) {
      debugPrint('Error loading user from preferences: $e');
    }
  }

  Future<void> _saveUserToPreferences(models.User user) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setInt('user_id', user.id!);
    } catch (e) {
      debugPrint('Error saving user to preferences: $e');
    }
  }

  Future<void> _clearUserFromPreferences() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.remove('user_id');
    } catch (e) {
      debugPrint('Error clearing user from preferences: $e');
    }
  }

  String _hashPassword(String password) {
    final bytes = utf8.encode(password);
    final digest = sha256.convert(bytes);
    return digest.toString();
  }

  Future<bool> signIn(String email, String password) async {
    try {
      _setLoading(true);

      final user = await _databaseService.getUserByEmail(email);
      if (user == null) {
        debugPrint('User not found');
        return false;
      }

      final hashedPassword = _hashPassword(password);
      if (user.passwordHash != hashedPassword) {
        debugPrint('Invalid password');
        return false;
      }

      _user = user.copyWith(lastLogin: DateTime.now());
      await _databaseService.updateUser(_user!);
      _userRole = _user!.role == 'doctor' ? UserRole.doctor : UserRole.patient;

      await _saveUserToPreferences(_user!);
      notifyListeners();
      return true;
    } catch (e) {
      debugPrint('Sign in error: $e');
      return false;
    } finally {
      _setLoading(false);
    }
  }

  Future<bool> signUp(
    String email,
    String password,
    String name,
    UserRole role,
  ) async {
    try {
      _setLoading(true);

      // Check if user already exists
      final existingUser = await _databaseService.getUserByEmail(email);
      if (existingUser != null) {
        debugPrint('User already exists');
        return false;
      }

      final hashedPassword = _hashPassword(password);
      final newUser = models.User(
        email: email,
        passwordHash: hashedPassword,
        name: name,
        role: role == UserRole.doctor ? 'doctor' : 'patient',
        createdAt: DateTime.now(),
      );

      final userId = await _databaseService.insertUser(newUser);
      _user = newUser.copyWith(id: userId);
      _userRole = role;

      await _saveUserToPreferences(_user!);
      notifyListeners();
      return true;
    } catch (e) {
      debugPrint('Sign up error: $e');
      return false;
    } finally {
      _setLoading(false);
    }
  }

  Future<void> signOut() async {
    try {
      _setLoading(true);
      await _clearUserFromPreferences();
      _user = null;
      _userRole = null;
      notifyListeners();
    } catch (e) {
      debugPrint('Sign out error: $e');
    } finally {
      _setLoading(false);
    }
  }
}
