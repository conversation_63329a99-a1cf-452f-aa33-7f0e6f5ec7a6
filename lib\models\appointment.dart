class Appointment {
  final int? id;
  final int? slotId;
  final int patientId;
  final int doctorId;
  final DateTime dateTime;
  final int durationMinutes;
  final String patientName;
  final String doctorName;
  final String status; // scheduled, completed, cancelled, no-show
  final String? symptoms;
  final String? notes;
  final DateTime? followUpDate;
  final DateTime createdAt;
  final DateTime lastUpdated;
  final String paymentStatus; // pending, completed, refunded
  final double paymentAmount;
  final String? paymentMethod;
  final String meetingType; // in-person, video, phone
  final String? meetingLink;

  Appointment({
    this.id,
    this.slotId,
    required this.patientId,
    required this.doctorId,
    required this.dateTime,
    this.durationMinutes = 30,
    required this.patientName,
    required this.doctorName,
    this.status = 'scheduled',
    this.symptoms,
    this.notes,
    this.followUpDate,
    required this.createdAt,
    required this.lastUpdated,
    this.paymentStatus = 'pending',
    this.paymentAmount = 0.0,
    this.paymentMethod,
    this.meetingType = 'in-person',
    this.meetingLink,
  });

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'slot_id': slotId,
      'patient_id': patientId,
      'doctor_id': doctorId,
      'date_time': dateTime.millisecondsSinceEpoch,
      'duration_minutes': durationMinutes,
      'patient_name': patientName,
      'doctor_name': doctorName,
      'status': status,
      'symptoms': symptoms,
      'notes': notes,
      'follow_up_date': followUpDate?.millisecondsSinceEpoch,
      'created_at': createdAt.millisecondsSinceEpoch,
      'last_updated': lastUpdated.millisecondsSinceEpoch,
      'payment_status': paymentStatus,
      'payment_amount': paymentAmount,
      'payment_method': paymentMethod,
      'meeting_type': meetingType,
      'meeting_link': meetingLink,
    };
  }

  factory Appointment.fromMap(Map<String, dynamic> map) {
    return Appointment(
      id: map['id']?.toInt(),
      slotId: map['slot_id']?.toInt(),
      patientId: map['patient_id']?.toInt() ?? 0,
      doctorId: map['doctor_id']?.toInt() ?? 0,
      dateTime: DateTime.fromMillisecondsSinceEpoch(map['date_time']),
      durationMinutes: map['duration_minutes']?.toInt() ?? 30,
      patientName: map['patient_name'] ?? '',
      doctorName: map['doctor_name'] ?? '',
      status: map['status'] ?? 'scheduled',
      symptoms: map['symptoms'],
      notes: map['notes'],
      followUpDate: map['follow_up_date'] != null 
          ? DateTime.fromMillisecondsSinceEpoch(map['follow_up_date'])
          : null,
      createdAt: DateTime.fromMillisecondsSinceEpoch(map['created_at']),
      lastUpdated: DateTime.fromMillisecondsSinceEpoch(map['last_updated']),
      paymentStatus: map['payment_status'] ?? 'pending',
      paymentAmount: map['payment_amount']?.toDouble() ?? 0.0,
      paymentMethod: map['payment_method'],
      meetingType: map['meeting_type'] ?? 'in-person',
      meetingLink: map['meeting_link'],
    );
  }
}
