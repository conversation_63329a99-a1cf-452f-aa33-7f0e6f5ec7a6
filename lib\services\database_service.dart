import 'package:sqflite/sqflite.dart';
import 'package:path/path.dart';
import 'package:flutter/material.dart';
import '../models/user.dart';
import '../models/doctor.dart';
import '../models/patient.dart';
import '../models/appointment.dart';
import '../models/slot.dart';

class DatabaseService {
  static final DatabaseService _instance = DatabaseService._internal();
  factory DatabaseService() => _instance;
  DatabaseService._internal();

  static Database? _database;

  Future<Database> get database async {
    if (_database != null) return _database!;
    _database = await _initDatabase();
    return _database!;
  }

  Future<Database> _initDatabase() async {
    String path = join(await getDatabasesPath(), 'doctor_appointment.db');

    return await openDatabase(path, version: 1, onCreate: _createTables);
  }

  Future<void> _createTables(Database db, int version) async {
    // Users table
    await db.execute('''
      CREATE TABLE users (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        email TEXT UNIQUE NOT NULL,
        password_hash TEXT NOT NULL,
        name TEXT NOT NULL,
        role TEXT NOT NULL,
        created_at INTEGER NOT NULL,
        last_login INTEGER,
        is_active INTEGER DEFAULT 1,
        profile_image_url TEXT,
        phone_number TEXT
      )
    ''');

    // Doctors table
    await db.execute('''
      CREATE TABLE doctors (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        user_id INTEGER NOT NULL,
        name TEXT NOT NULL,
        specialization TEXT NOT NULL,
        experience INTEGER NOT NULL,
        qualifications TEXT NOT NULL,
        bio TEXT NOT NULL,
        profile_image_url TEXT,
        clinic_address TEXT NOT NULL,
        consultation_fee REAL NOT NULL,
        rating REAL DEFAULT 0.0,
        total_reviews INTEGER DEFAULT 0,
        available_days TEXT NOT NULL,
        working_hours_start TEXT NOT NULL,
        working_hours_end TEXT NOT NULL,
        created_at INTEGER NOT NULL,
        is_verified INTEGER DEFAULT 0,
        is_active INTEGER DEFAULT 1,
        contact_email TEXT NOT NULL,
        contact_phone TEXT NOT NULL,
        FOREIGN KEY (user_id) REFERENCES users (id)
      )
    ''');

    // Patients table
    await db.execute('''
      CREATE TABLE patients (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        user_id INTEGER NOT NULL,
        name TEXT NOT NULL,
        date_of_birth INTEGER NOT NULL,
        gender TEXT NOT NULL,
        blood_group TEXT NOT NULL,
        allergies TEXT,
        chronic_diseases TEXT,
        emergency_contact_name TEXT NOT NULL,
        emergency_contact_relationship TEXT NOT NULL,
        emergency_contact_phone TEXT NOT NULL,
        address TEXT NOT NULL,
        profile_image_url TEXT,
        created_at INTEGER NOT NULL,
        last_updated INTEGER NOT NULL,
        contact_email TEXT NOT NULL,
        contact_phone TEXT NOT NULL,
        insurance_provider TEXT,
        insurance_policy_number TEXT,
        insurance_expiry_date INTEGER,
        FOREIGN KEY (user_id) REFERENCES users (id)
      )
    ''');

    // Slots table
    await db.execute('''
      CREATE TABLE slots (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        doctor_id INTEGER NOT NULL,
        date_time INTEGER NOT NULL,
        duration_minutes INTEGER DEFAULT 30,
        is_booked INTEGER DEFAULT 0,
        patient_id INTEGER,
        created_at INTEGER NOT NULL,
        FOREIGN KEY (doctor_id) REFERENCES doctors (id),
        FOREIGN KEY (patient_id) REFERENCES patients (id)
      )
    ''');

    // Appointments table
    await db.execute('''
      CREATE TABLE appointments (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        slot_id INTEGER,
        patient_id INTEGER NOT NULL,
        doctor_id INTEGER NOT NULL,
        date_time INTEGER NOT NULL,
        duration_minutes INTEGER DEFAULT 30,
        patient_name TEXT NOT NULL,
        doctor_name TEXT NOT NULL,
        status TEXT DEFAULT 'scheduled',
        symptoms TEXT,
        notes TEXT,
        follow_up_date INTEGER,
        created_at INTEGER NOT NULL,
        last_updated INTEGER NOT NULL,
        payment_status TEXT DEFAULT 'pending',
        payment_amount REAL DEFAULT 0.0,
        payment_method TEXT,
        meeting_type TEXT DEFAULT 'in-person',
        meeting_link TEXT,
        FOREIGN KEY (slot_id) REFERENCES slots (id),
        FOREIGN KEY (patient_id) REFERENCES patients (id),
        FOREIGN KEY (doctor_id) REFERENCES doctors (id)
      )
    ''');

    debugPrint('Database tables created successfully');
  }

  // User operations
  Future<int> insertUser(User user) async {
    final db = await database;
    return await db.insert('users', user.toMap());
  }

  Future<User?> getUserByEmail(String email) async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query(
      'users',
      where: 'email = ?',
      whereArgs: [email],
    );

    if (maps.isNotEmpty) {
      return User.fromMap(maps.first);
    }
    return null;
  }

  Future<User?> getUserById(int id) async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query(
      'users',
      where: 'id = ?',
      whereArgs: [id],
    );

    if (maps.isNotEmpty) {
      return User.fromMap(maps.first);
    }
    return null;
  }

  Future<int> updateUser(User user) async {
    final db = await database;
    return await db.update(
      'users',
      user.toMap(),
      where: 'id = ?',
      whereArgs: [user.id],
    );
  }

  // Doctor operations
  Future<int> insertDoctor(Doctor doctor) async {
    final db = await database;
    return await db.insert('doctors', doctor.toMap());
  }

  Future<Doctor?> getDoctorByUserId(int userId) async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query(
      'doctors',
      where: 'user_id = ?',
      whereArgs: [userId],
    );

    if (maps.isNotEmpty) {
      return Doctor.fromMap(maps.first);
    }
    return null;
  }

  Future<Doctor?> getDoctorById(int id) async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query(
      'doctors',
      where: 'id = ?',
      whereArgs: [id],
    );

    if (maps.isNotEmpty) {
      return Doctor.fromMap(maps.first);
    }
    return null;
  }

  Future<List<Doctor>> getAllDoctors() async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query('doctors');
    return List.generate(maps.length, (i) => Doctor.fromMap(maps[i]));
  }

  // Patient operations
  Future<int> insertPatient(Patient patient) async {
    final db = await database;
    return await db.insert('patients', patient.toMap());
  }

  Future<Patient?> getPatientByUserId(int userId) async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query(
      'patients',
      where: 'user_id = ?',
      whereArgs: [userId],
    );

    if (maps.isNotEmpty) {
      return Patient.fromMap(maps.first);
    }
    return null;
  }

  Future<Patient?> getPatientById(int id) async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query(
      'patients',
      where: 'id = ?',
      whereArgs: [id],
    );

    if (maps.isNotEmpty) {
      return Patient.fromMap(maps.first);
    }
    return null;
  }

  // Slot operations
  Future<int> insertSlot(Slot slot) async {
    final db = await database;
    return await db.insert('slots', slot.toMap());
  }

  Future<List<Slot>> getAvailableSlots(int doctorId) async {
    final db = await database;
    final now = DateTime.now().millisecondsSinceEpoch;
    final List<Map<String, dynamic>> maps = await db.query(
      'slots',
      where: 'doctor_id = ? AND is_booked = 0 AND date_time >= ?',
      whereArgs: [doctorId, now],
      orderBy: 'date_time ASC',
    );
    return List.generate(maps.length, (i) => Slot.fromMap(maps[i]));
  }

  Future<Slot?> getSlotById(int id) async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query(
      'slots',
      where: 'id = ?',
      whereArgs: [id],
    );

    if (maps.isNotEmpty) {
      return Slot.fromMap(maps.first);
    }
    return null;
  }

  Future<int> updateSlot(Slot slot) async {
    final db = await database;
    return await db.update(
      'slots',
      slot.toMap(),
      where: 'id = ?',
      whereArgs: [slot.id],
    );
  }

  // Appointment operations
  Future<int> insertAppointment(Appointment appointment) async {
    final db = await database;
    return await db.insert('appointments', appointment.toMap());
  }

  Future<List<Appointment>> getAppointmentsByPatientId(int patientId) async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query(
      'appointments',
      where: 'patient_id = ?',
      whereArgs: [patientId],
      orderBy: 'date_time DESC',
    );
    return List.generate(maps.length, (i) => Appointment.fromMap(maps[i]));
  }

  Future<List<Appointment>> getAppointmentsByDoctorId(int doctorId) async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query(
      'appointments',
      where: 'doctor_id = ?',
      whereArgs: [doctorId],
      orderBy: 'date_time DESC',
    );
    return List.generate(maps.length, (i) => Appointment.fromMap(maps[i]));
  }

  Future<List<Appointment>> getUpcomingAppointments(int patientId) async {
    final db = await database;
    final now = DateTime.now().millisecondsSinceEpoch;
    final List<Map<String, dynamic>> maps = await db.query(
      'appointments',
      where: 'patient_id = ? AND date_time >= ?',
      whereArgs: [patientId, now],
      orderBy: 'date_time ASC',
      limit: 10,
    );
    return List.generate(maps.length, (i) => Appointment.fromMap(maps[i]));
  }

  Future<List<Appointment>> getTodayAppointments(int doctorId) async {
    final db = await database;
    final now = DateTime.now();
    final startOfDay =
        DateTime(now.year, now.month, now.day).millisecondsSinceEpoch;
    final endOfDay =
        DateTime(
          now.year,
          now.month,
          now.day,
          23,
          59,
          59,
        ).millisecondsSinceEpoch;

    final List<Map<String, dynamic>> maps = await db.query(
      'appointments',
      where: 'doctor_id = ? AND date_time >= ? AND date_time <= ?',
      whereArgs: [doctorId, startOfDay, endOfDay],
      orderBy: 'date_time ASC',
    );
    return List.generate(maps.length, (i) => Appointment.fromMap(maps[i]));
  }

  Future<int> updateAppointment(Appointment appointment) async {
    final db = await database;
    return await db.update(
      'appointments',
      appointment.toMap(),
      where: 'id = ?',
      whereArgs: [appointment.id],
    );
  }

  // Utility methods
  Future<void> clearAllData() async {
    final db = await database;
    await db.delete('appointments');
    await db.delete('slots');
    await db.delete('patients');
    await db.delete('doctors');
    await db.delete('users');
  }

  Future<void> close() async {
    final db = await database;
    await db.close();
  }
}
